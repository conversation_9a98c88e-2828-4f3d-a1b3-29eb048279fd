#!/usr/bin/env python3
"""
跨平台启动脚本 - 语音识别后端服务

支持 Windows、macOS、Linux
使用方法: python start_server.py
"""

import os
import sys
import platform
import subprocess
import shutil
from pathlib import Path


def print_banner():
    """打印启动横幅"""
    print("=" * 50)
    print("语音识别系统后端服务启动脚本")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {platform.python_version()}")
    print("=" * 50)
    print()


def check_python():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {platform.python_version()}")
        return False
    
    print(f"✅ Python版本检查通过: {platform.python_version()}")
    return True


def check_uv():
    """检查uv包管理器"""
    try:
        result = subprocess.run(['uv', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ uv包管理器可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ uv包管理器不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ uv包管理器未安装")
        print("请运行: pip install uv")
        return False


def setup_ssl_certificates():
    """设置SSL证书"""
    try:
        import certifi
        cert_path = certifi.where()
        
        os.environ['SSL_CERT_FILE'] = cert_path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['CURL_CA_BUNDLE'] = cert_path
        
        print(f"✅ SSL证书已配置: {cert_path}")
        return True
    except ImportError:
        print("⚠️  警告: certifi包未安装")
        print("请运行: pip install certifi")
        return False
    except Exception as e:
        print(f"⚠️  警告: SSL证书配置失败: {e}")
        return False


def check_ffmpeg():
    """检查ffmpeg可用性"""
    ffmpeg_cmd = 'ffmpeg.exe' if platform.system() == 'Windows' else 'ffmpeg'
    
    if shutil.which(ffmpeg_cmd) is None:
        print("⚠️  警告: ffmpeg未安装或不在PATH中")
        print("音频格式转换功能可能无法正常工作")
        print("建议安装ffmpeg: https://ffmpeg.org/download.html")
        return False
    
    try:
        result = subprocess.run([ffmpeg_cmd, '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ ffmpeg可用: {version_line}")
            return True
        else:
            print("⚠️  警告: ffmpeg执行失败")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠️  警告: ffmpeg检查超时")
        return False


def check_config_file():
    """检查配置文件"""
    config_file = Path("config.yaml")
    if not config_file.exists():
        print("❌ 错误: 配置文件 config.yaml 不存在")
        print("请确保配置文件存在并包含正确的阿里云API Key")
        return False
    
    print("✅ 配置文件检查通过")
    return True


def start_server():
    """启动服务器"""
    print("\n🚀 启动语音识别后端服务...")
    print("服务地址: http://localhost:8001")
    print("API文档: http://localhost:8001/docs")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # 启动服务
        subprocess.run([
            'uv', 'run', 'uvicorn', 'app.main:app',
            '--reload', '--host', '0.0.0.0', '--port', '8001'
        ])
    except KeyboardInterrupt:
        print("\n\n🛑 服务已停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print_banner()
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"工作目录: {script_dir.absolute()}")
    print()
    
    # 系统检查
    checks = [
        ("Python版本", check_python),
        ("uv包管理器", check_uv),
        ("SSL证书", setup_ssl_certificates),
        ("ffmpeg", check_ffmpeg),
        ("配置文件", check_config_file),
    ]
    
    failed_checks = []
    for name, check_func in checks:
        try:
            if not check_func():
                failed_checks.append(name)
        except Exception as e:
            print(f"❌ {name}检查失败: {e}")
            failed_checks.append(name)
    
    # 检查是否有关键错误
    critical_checks = ["Python版本", "uv包管理器", "配置文件"]
    critical_failures = [check for check in failed_checks if check in critical_checks]
    
    if critical_failures:
        print(f"\n❌ 关键检查失败: {', '.join(critical_failures)}")
        print("请解决上述问题后重新运行")
        input("按Enter键退出...")
        return 1
    
    if failed_checks:
        print(f"\n⚠️  警告: 部分检查失败: {', '.join(failed_checks)}")
        print("服务可能无法完全正常工作")
        
        response = input("是否继续启动服务? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("启动已取消")
            return 0
    
    # 启动服务
    if not start_server():
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
