# PowerShell 启动脚本 - 语音识别后端服务
# 使用方法: 在PowerShell中运行 .\start_backend.ps1

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "语音识别系统后端服务启动脚本 (Windows)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 切换到脚本所在目录
Set-Location $PSScriptRoot
Write-Host "当前目录: $(Get-Location)" -ForegroundColor Green

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: Python未安装或不在PATH中" -ForegroundColor Red
    Write-Host "请安装Python 3.8或更高版本" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

# 检查uv是否安装
try {
    $uvVersion = uv --version 2>&1
    Write-Host "uv版本: $uvVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: uv包管理器未安装" -ForegroundColor Red
    Write-Host "请运行: pip install uv" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

# 设置SSL证书环境变量
Write-Host "设置SSL证书环境变量..." -ForegroundColor Yellow
try {
    $certPath = python -c "import certifi; print(certifi.where())"
    $env:SSL_CERT_FILE = $certPath
    $env:REQUESTS_CA_BUNDLE = $certPath
    $env:CURL_CA_BUNDLE = $certPath
    Write-Host "SSL证书路径: $certPath" -ForegroundColor Green
} catch {
    Write-Host "警告: 无法设置SSL证书路径" -ForegroundColor Yellow
    Write-Host "请确保certifi包已安装: pip install certifi" -ForegroundColor Yellow
}

# 检查ffmpeg是否可用
try {
    $ffmpegVersion = ffmpeg -version 2>&1 | Select-Object -First 1
    Write-Host "ffmpeg可用: $($ffmpegVersion.Split(' ')[2])" -ForegroundColor Green
} catch {
    Write-Host "警告: ffmpeg未安装或不在PATH中" -ForegroundColor Yellow
    Write-Host "音频格式转换功能可能无法正常工作" -ForegroundColor Yellow
    Write-Host "建议安装ffmpeg: https://ffmpeg.org/download.html" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "按任意键继续启动服务..." -ForegroundColor Cyan
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# 检查配置文件
if (-not (Test-Path "config.yaml")) {
    Write-Host "错误: 配置文件 config.yaml 不存在" -ForegroundColor Red
    Write-Host "请确保配置文件存在并包含正确的阿里云API Key" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host ""
Write-Host "启动语音识别后端服务..." -ForegroundColor Cyan
Write-Host "服务地址: http://localhost:8001" -ForegroundColor Green
Write-Host "API文档: http://localhost:8001/docs" -ForegroundColor Green
Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host ""

# 启动服务
try {
    uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
} catch {
    Write-Host ""
    Write-Host "服务启动失败: $_" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "服务已停止" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
}
