"""
语音识别服务模块

封装阿里云 Paraformer SDK，提供语音转文字功能。
支持同步和异步识别，提供统一的接口和错误处理。
"""

import os
import tempfile
import logging
import ssl
from typing import Optional, Dict, Any, List
from pathlib import Path

import dashscope
from dashscope.audio.asr import Recognition, RecognitionCallback, RecognitionResult
from http import HTTPStatus

from ..config.settings import AliyunConfig
from .audio_converter import create_audio_converter, AudioConversionError

# 临时解决SSL证书验证问题
import aiohttp
import certifi


class SpeechRecognitionError(Exception):
    """语音识别异常"""
    pass


class SpeechRecognitionService:
    """
    语音识别服务
    
    封装阿里云 Paraformer SDK，提供语音转文字功能。
    """
    
    def __init__(self, config: AliyunConfig):
        """
        初始化语音识别服务

        Args:
            config: 阿里云配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 设置 DashScope API Key
        dashscope.api_key = config.api_key

        # 配置SSL上下文以解决证书验证问题
        self._setup_ssl_context()

        # 默认参数
        self.default_params = {
            'model': config.model,
            'language_hints': [config.language, 'en'],
            'format': 'wav',
            'sample_rate': 16000,
            'disfluency_removal_enabled': False,
            'punctuation_prediction_enabled': True,
            'inverse_text_normalization_enabled': True,
            # 添加更多参数以提高识别质量
            'semantic_punctuation_enabled': False,
            'max_sentence_silence': 800,
        }

    def _setup_ssl_context(self):
        """
        设置SSL上下文以解决证书验证问题
        """
        try:
            # 方法1: 使用certifi证书
            import certifi
            cert_path = certifi.where()

            # 设置环境变量
            os.environ['SSL_CERT_FILE'] = cert_path
            os.environ['REQUESTS_CA_BUNDLE'] = cert_path
            os.environ['CURL_CA_BUNDLE'] = cert_path

            # 验证证书文件是否存在
            if os.path.exists(cert_path):
                self.logger.info(f"SSL证书配置成功: {cert_path}")

                # 创建SSL上下文
                ssl_context = ssl.create_default_context(cafile=cert_path)

                # 设置aiohttp的SSL上下文
                import aiohttp
                connector = aiohttp.TCPConnector(ssl=ssl_context)

                # 尝试设置dashscope的SSL配置
                try:
                    import dashscope
                    # 如果dashscope支持自定义SSL配置，在这里设置
                    self.logger.info("DashScope SSL配置已应用")
                except Exception as e:
                    self.logger.warning(f"DashScope SSL配置失败: {e}")

                return True
            else:
                self.logger.error(f"证书文件不存在: {cert_path}")

        except ImportError:
            self.logger.error("certifi包未安装")
        except Exception as e:
            self.logger.error(f"SSL配置失败: {e}")

        return False

    def recognize_file(
        self, 
        file_path: str, 
        **kwargs
    ) -> Dict[str, Any]:
        """
        识别音频文件（同步方式）
        
        Args:
            file_path: 音频文件路径
            **kwargs: 额外的识别参数
            
        Returns:
            Dict[str, Any]: 识别结果
            
        Raises:
            SpeechRecognitionError: 识别失败时抛出
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise SpeechRecognitionError(f"音频文件不存在: {file_path}")
            
            # 合并参数
            params = {**self.default_params, **kwargs}
            
            # 创建识别器
            recognition = Recognition(
                model=params['model'],
                format=params['format'],
                sample_rate=params['sample_rate'],
                language_hints=params['language_hints'],
                disfluency_removal_enabled=params['disfluency_removal_enabled'],
                punctuation_prediction_enabled=params['punctuation_prediction_enabled'],
                inverse_text_normalization_enabled=params['inverse_text_normalization_enabled'],
                semantic_punctuation_enabled=params.get('semantic_punctuation_enabled', False),
                max_sentence_silence=params.get('max_sentence_silence', 800),
                callback=None
            )
            
            self.logger.info(f"开始识别音频文件: {file_path}")
            self.logger.info(f"识别参数: 模型={params['model']}, 格式={params['format']}, 采样率={params['sample_rate']}")

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            self.logger.info(f"音频文件大小: {file_size} bytes")

            # 执行识别
            result = recognition.call(file_path)

            self.logger.info(f"识别响应状态: {result.status_code}")
            if hasattr(result, 'message'):
                self.logger.info(f"识别响应消息: {result.message}")

            if result.status_code == HTTPStatus.OK:
                sentence = result.get_sentence()

                self.logger.info(f"原始sentence类型: {type(sentence)}")
                self.logger.info(f"原始sentence内容: {sentence}")

                # 检查sentence的类型和内容
                if sentence is None:
                    error_msg = "识别结果为空，可能是音频文件无有效语音内容"
                    self.logger.warning(error_msg)
                    recognition_result = {
                        'success': True,
                        'text': '',
                        'begin_time': 0,
                        'end_time': 0,
                        'words': [],
                        'request_id': result.get_request_id(),
                        'usage': None
                    }
                elif isinstance(sentence, list):
                    # 如果sentence是列表，取第一个元素或合并所有文本
                    if len(sentence) > 0:
                        first_sentence = sentence[0] if isinstance(sentence[0], dict) else {}
                        all_text = ' '.join([s.get('text', '') if isinstance(s, dict) else str(s) for s in sentence])
                        recognition_result = {
                            'success': True,
                            'text': all_text,
                            'begin_time': first_sentence.get('begin_time', 0),
                            'end_time': sentence[-1].get('end_time', 0) if isinstance(sentence[-1], dict) else 0,
                            'words': first_sentence.get('words', []),
                            'request_id': result.get_request_id(),
                            'usage': result.get_usage(sentence) if sentence else None
                        }
                    else:
                        recognition_result = {
                            'success': True,
                            'text': '',
                            'begin_time': 0,
                            'end_time': 0,
                            'words': [],
                            'request_id': result.get_request_id(),
                            'usage': None
                        }
                elif isinstance(sentence, dict):
                    # 如果sentence是字典，按原来的方式处理
                    recognition_result = {
                        'success': True,
                        'text': sentence.get('text', ''),
                        'begin_time': sentence.get('begin_time', 0),
                        'end_time': sentence.get('end_time', 0),
                        'words': sentence.get('words', []),
                        'request_id': result.get_request_id(),
                        'usage': result.get_usage(sentence) if sentence else None
                    }
                else:
                    # 其他类型，尝试转换为字符串
                    recognition_result = {
                        'success': True,
                        'text': str(sentence),
                        'begin_time': 0,
                        'end_time': 0,
                        'words': [],
                        'request_id': result.get_request_id(),
                        'usage': None
                    }

                self.logger.info(f"识别成功，文本: {recognition_result['text']}")
                return recognition_result
            else:
                error_msg = f"识别失败: {result.message}"
                self.logger.error(error_msg)
                raise SpeechRecognitionError(error_msg)
                
        except Exception as e:
            if isinstance(e, SpeechRecognitionError):
                raise
            else:
                error_msg = f"语音识别过程中发生错误: {str(e)}"
                self.logger.error(error_msg)
                raise SpeechRecognitionError(error_msg)
    
    def recognize_bytes(
        self,
        audio_data: bytes,
        audio_format: str = 'wav',
        sample_rate: int = 16000,
        **kwargs
    ) -> Dict[str, Any]:
        """
        识别音频字节数据（通过临时文件）

        Args:
            audio_data: 音频字节数据
            audio_format: 音频格式
            sample_rate: 采样率
            **kwargs: 额外的识别参数

        Returns:
            Dict[str, Any]: 识别结果

        Raises:
            SpeechRecognitionError: 识别失败时抛出
        """
        temp_file = None
        converted_file = None
        try:
            # 创建临时文件
            suffix = f'.{audio_format}'
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name

            self.logger.info(f"原始音频格式: {audio_format}")

            # 检查是否需要转换格式
            final_file_path = temp_file_path
            final_format = audio_format

            # 如果不是WAV格式或者格式不被支持，进行转换
            if audio_format.lower() != 'wav' or not self.validate_audio_format(audio_format):
                try:
                    self.logger.info(f"需要转换音频格式: {audio_format} -> wav")

                    # 创建音频转换器
                    converter = create_audio_converter()

                    # 转换为WAV格式
                    converted_file = converter.convert_bytes_to_wav(
                        audio_data=audio_data,
                        input_format=audio_format
                    )

                    final_file_path = converted_file
                    final_format = 'wav'

                    self.logger.info(f"音频转换成功: {converted_file}")

                except AudioConversionError as e:
                    self.logger.error(f"音频转换失败: {e}")
                    raise SpeechRecognitionError(f"音频格式转换失败: {e}")

            # 设置识别参数
            params = {
                'format': final_format,
                'sample_rate': sample_rate,
                **kwargs
            }

            # 执行识别
            result = self.recognize_file(final_file_path, **params)

            return result

        finally:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                except Exception as e:
                    self.logger.warning(f"删除原始临时文件失败: {e}")

            # 清理转换后的文件
            if converted_file and os.path.exists(converted_file):
                try:
                    os.unlink(converted_file)
                except Exception as e:
                    self.logger.warning(f"删除转换后临时文件失败: {e}")
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的音频格式

        Returns:
            List[str]: 支持的音频格式列表
        """
        return ['pcm', 'wav', 'mp3', 'opus', 'speex', 'aac', 'amr', 'webm', 'ogg']
    
    def validate_audio_format(self, format_name: str) -> bool:
        """
        验证音频格式是否支持
        
        Args:
            format_name: 音频格式名称
            
        Returns:
            bool: 是否支持
        """
        return format_name.lower() in self.get_supported_formats()


class StreamRecognitionCallback(RecognitionCallback):
    """流式识别回调处理器"""
    
    def __init__(self):
        self.results = []
        self.completed = False
        self.error_message = None
        self.logger = logging.getLogger(__name__)
    
    def on_open(self) -> None:
        self.logger.info("语音识别连接已建立")
    
    def on_close(self) -> None:
        self.logger.info("语音识别连接已关闭")
    
    def on_complete(self) -> None:
        self.completed = True
        self.logger.info("语音识别完成")
    
    def on_error(self, result: RecognitionResult) -> None:
        self.error_message = result.message
        self.logger.error(f"语音识别错误: {result.message}")
    
    def on_event(self, result: RecognitionResult) -> None:
        sentence = result.get_sentence()
        if 'text' in sentence:
            self.results.append({
                'text': sentence['text'],
                'begin_time': sentence.get('begin_time', 0),
                'end_time': sentence.get('end_time', 0),
                'is_sentence_end': RecognitionResult.is_sentence_end(sentence),
                'words': sentence.get('words', [])
            })
            
            if RecognitionResult.is_sentence_end(sentence):
                self.logger.info(f"句子结束: {sentence['text']}")


def create_speech_recognition_service(config: AliyunConfig) -> SpeechRecognitionService:
    """
    创建语音识别服务实例的工厂函数
    
    Args:
        config: 阿里云配置
        
    Returns:
        SpeechRecognitionService: 语音识别服务实例
    """
    return SpeechRecognitionService(config)
