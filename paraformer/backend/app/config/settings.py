"""
配置管理模块

提供应用配置的加载、验证和访问功能。
支持从 YAML 文件加载配置，并提供类型安全的配置访问。
"""

import os
import yaml
from typing import List, Optional
from pydantic import BaseModel, Field
from pathlib import Path


class AliyunConfig(BaseModel):
    """阿里云配置"""
    api_key: str = Field(..., description="阿里云 API Key")
    model: str = Field(default="paraformer-realtime-v2", description="模型名称")
    language: str = Field(default="zh", description="语言设置")


class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    debug: bool = Field(default=True, description="调试模式")


class UploadConfig(BaseModel):
    """文件上传配置"""
    max_file_size: int = Field(default=50, description="最大文件大小 (MB)")
    allowed_formats: List[str] = Field(
        default=["wav", "mp3", "m4a", "flac"], 
        description="允许的文件格式"
    )
    temp_dir: str = Field(default="./temp", description="临时文件存储目录")


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )


class Settings(BaseModel):
    """应用设置"""
    aliyun: AliyunConfig
    server: ServerConfig = Field(default_factory=ServerConfig)
    upload: UploadConfig = Field(default_factory=UploadConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为 config.yaml
        """
        if config_path is None:
            # 获取项目根目录下的配置文件
            current_dir = Path(__file__).parent.parent.parent
            config_path = current_dir / "config.yaml"
        
        self.config_path = Path(config_path)
        self._settings: Optional[Settings] = None
    
    def load_config(self) -> Settings:
        """
        加载配置文件
        
        Returns:
            Settings: 配置对象
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML 格式错误
            ValueError: 配置验证失败
        """
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            self._settings = Settings(**config_data)
            return self._settings
            
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise ValueError(f"配置验证失败: {e}")
    
    @property
    def settings(self) -> Settings:
        """
        获取配置设置
        
        Returns:
            Settings: 配置对象
        """
        if self._settings is None:
            self._settings = self.load_config()
        return self._settings
    
    def get_aliyun_config(self) -> AliyunConfig:
        """获取阿里云配置"""
        return self.settings.aliyun
    
    def get_server_config(self) -> ServerConfig:
        """获取服务器配置"""
        return self.settings.server
    
    def get_upload_config(self) -> UploadConfig:
        """获取上传配置"""
        return self.settings.upload
    
    def get_logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        return self.settings.logging


# 全局配置管理器实例
config_manager = ConfigManager()


def get_settings() -> Settings:
    """
    获取应用设置的便捷函数
    
    Returns:
        Settings: 配置对象
    """
    return config_manager.settings
