# 语音识别系统配置文件

# 阿里云配置
aliyun:
  # 阿里云 API Key
  api_key: "sk-9f44f8543c584cc0874b064fc1f6784f"
  # 模型名称
  model: "paraformer-realtime-v2"
  # 语言设置
  language: "zh"

# 服务器配置
server:
  # 服务器主机
  host: "0.0.0.0"
  # 服务器端口
  port: 8000
  # 调试模式
  debug: true

# 文件上传配置
upload:
  # 最大文件大小 (MB)
  max_file_size: 50
  # 允许的文件格式
  allowed_formats: ["wav", "mp3", "m4a", "flac", "webm", "ogg"]
  # 临时文件存储目录
  temp_dir: "./temp"

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
