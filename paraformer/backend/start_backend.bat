@echo off
REM Windows 启动脚本 - 语音识别后端服务

echo ========================================
echo 语音识别系统后端服务启动脚本 (Windows)
echo ========================================

REM 切换到脚本所在目录
cd /d "%~dp0"

echo 当前目录: %CD%

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    echo 请安装Python 3.8或更高版本
    pause
    exit /b 1
)

REM 检查uv是否安装
uv --version >nul 2>&1
if errorlevel 1 (
    echo 错误: uv包管理器未安装
    echo 请运行: pip install uv
    pause
    exit /b 1
)

REM 设置SSL证书环境变量
echo 设置SSL证书环境变量...
for /f "delims=" %%i in ('python -c "import certifi; print(certifi.where())"') do set SSL_CERT_FILE=%%i
set REQUESTS_CA_BUNDLE=%SSL_CERT_FILE%
set CURL_CA_BUNDLE=%SSL_CERT_FILE%

echo SSL证书路径: %SSL_CERT_FILE%

REM 检查ffmpeg是否可用
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo 警告: ffmpeg未安装或不在PATH中
    echo 音频格式转换功能可能无法正常工作
    echo 建议安装ffmpeg: https://ffmpeg.org/download.html
    echo.
    echo 按任意键继续启动服务...
    pause >nul
) else (
    echo ffmpeg已安装，音频转换功能可用
)

REM 检查配置文件
if not exist "config.yaml" (
    echo 错误: 配置文件 config.yaml 不存在
    echo 请确保配置文件存在并包含正确的阿里云API Key
    pause
    exit /b 1
)

echo 启动语音识别后端服务...
echo 服务地址: http://localhost:8001
echo API文档: http://localhost:8001/docs
echo 按 Ctrl+C 停止服务
echo.

REM 启动服务
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8001

echo.
echo 服务已停止
pause
