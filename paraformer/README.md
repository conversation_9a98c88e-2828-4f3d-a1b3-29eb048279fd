# 语音识别系统

基于阿里云 Paraformer SDK 的完整语音识别系统，包含 Vue 3 前端和 Python 后端。

## 项目结构

```
paraformer/
├── backend/                 # Python 后端
│   ├── app/                # 应用主目录
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI 应用入口
│   │   ├── config/         # 配置管理
│   │   ├── services/       # 业务服务
│   │   └── api/           # API 路由
│   ├── pyproject.toml      # Python 依赖配置
│   └── config.yaml         # 应用配置文件
├── frontend/               # Vue 3 前端
│   ├── src/
│   │   ├── components/     # Vue 组件
│   │   ├── views/         # 页面视图
│   │   └── utils/         # 工具函数
│   ├── package.json
│   └── vite.config.js
└── README.md
```

## 技术栈

- **后端**: Python + FastAPI + 阿里云 Paraformer SDK
- **前端**: Vue 3 + Composition API + Vite
- **包管理**: uv (Python) + npm (Node.js)

## 功能特性

- 🎤 实时录音功能
- 🔄 音频文件上传
- 🗣️ 语音转文字识别
- 📱 响应式界面设计
- 🔧 模块化架构

## 快速开始

### 系统要求

- **Python**: 3.8或更高版本
- **Node.js**: 16或更高版本
- **uv**: Python包管理器 (`pip install uv`)
- **ffmpeg**: 音频转换工具（可选，用于音频格式转换）

### 后端启动

#### 方法1: 跨平台Python脚本（推荐）
```bash
cd backend
python start_server.py
```

#### 方法2: Windows批处理脚本
```cmd
cd backend
start_backend.bat
```

#### 方法3: Windows PowerShell脚本
```powershell
cd backend
.\start_backend.ps1
```

#### 方法4: 手动启动（macOS/Linux）
```bash
cd backend
export SSL_CERT_FILE=$(python3 -c "import certifi; print(certifi.where())")
export REQUESTS_CA_BUNDLE=$SSL_CERT_FILE
export CURL_CA_BUNDLE=$SSL_CERT_FILE
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
```

#### 方法5: 手动启动（Windows CMD）
```cmd
cd backend
for /f "delims=" %i in ('python -c "import certifi; print(certifi.where())"') do set SSL_CERT_FILE=%i
set REQUESTS_CA_BUNDLE=%SSL_CERT_FILE%
set CURL_CA_BUNDLE=%SSL_CERT_FILE%
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
```

### 前端启动
```bash
cd frontend
npm run dev
```

## 安装说明

### Windows用户

1. **安装Python**: 从 [python.org](https://www.python.org/downloads/) 下载并安装Python 3.8+
2. **安装uv**: 打开命令提示符，运行 `pip install uv`
3. **安装Node.js**: 从 [nodejs.org](https://nodejs.org/) 下载并安装
4. **安装ffmpeg**（可选）:
   - 从 [ffmpeg.org](https://ffmpeg.org/download.html) 下载
   - 或使用包管理器: `winget install ffmpeg` 或 `choco install ffmpeg`
   - 确保ffmpeg在PATH环境变量中

### macOS/Linux用户

1. **安装Python**: 使用系统包管理器或从官网下载
2. **安装uv**: `pip install uv`
3. **安装Node.js**: 使用包管理器或从官网下载
4. **安装ffmpeg**:
   - macOS: `brew install ffmpeg`
   - Ubuntu/Debian: `sudo apt install ffmpeg`
   - CentOS/RHEL: `sudo yum install ffmpeg`

## 配置说明

1. 复制 `backend/config.yaml.example` 为 `backend/config.yaml`
2. 在配置文件中填入阿里云 API Key
3. 启动后端和前端服务

## API 文档

启动后端服务后，访问 `http://localhost:8001/docs` 查看 API 文档。

## 故障排除

### Windows常见问题

1. **SSL证书错误**: 运行 `pip install --upgrade certifi`
2. **ffmpeg未找到**: 确保ffmpeg已安装并在PATH中
3. **PowerShell执行策略**: 运行 `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### 通用问题

1. **端口占用**: 修改启动脚本中的端口号（默认8001）
2. **依赖安装失败**: 尝试 `uv sync --reinstall`
3. **API Key错误**: 检查config.yaml中的阿里云API Key是否正确
