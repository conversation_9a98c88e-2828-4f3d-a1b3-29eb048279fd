@echo off
REM Windows 安装脚本 - 语音识别系统

echo ========================================
echo 语音识别系统 Windows 安装脚本
echo ========================================

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限
) else (
    echo 注意: 某些安装步骤可能需要管理员权限
)

echo.

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装
    echo 请从 https://www.python.org/downloads/ 下载并安装Python 3.8+
    echo 安装时请勾选 "Add Python to PATH"
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version') do echo ✅ Python已安装: %%i
)

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装
    echo 请从 https://nodejs.org/ 下载并安装Node.js
    pause
    exit /b 1
) else (
    for /f %%i in ('node --version') do echo ✅ Node.js已安装: %%i
)

REM 安装uv
echo.
echo 安装uv包管理器...
pip install uv
if errorlevel 1 (
    echo ❌ uv安装失败
    pause
    exit /b 1
) else (
    echo ✅ uv安装成功
)

REM 检查ffmpeg
echo.
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  ffmpeg未安装
    echo ffmpeg用于音频格式转换，建议安装
    echo 安装方法:
    echo 1. 从 https://ffmpeg.org/download.html 下载
    echo 2. 或使用包管理器: winget install ffmpeg
    echo 3. 或使用Chocolatey: choco install ffmpeg
    echo.
    set /p install_ffmpeg="是否现在尝试使用winget安装ffmpeg? (y/N): "
    if /i "%install_ffmpeg%"=="y" (
        echo 尝试使用winget安装ffmpeg...
        winget install ffmpeg
        if errorlevel 1 (
            echo ⚠️  winget安装失败，请手动安装ffmpeg
        ) else (
            echo ✅ ffmpeg安装成功
        )
    )
) else (
    echo ✅ ffmpeg已安装
)

REM 安装后端依赖
echo.
echo 安装后端依赖...
cd backend
if not exist "pyproject.toml" (
    echo ❌ 后端项目配置文件不存在
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

uv sync
if errorlevel 1 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
) else (
    echo ✅ 后端依赖安装成功
)

REM 检查配置文件
if not exist "config.yaml" (
    if exist "config.yaml.example" (
        echo 创建配置文件...
        copy "config.yaml.example" "config.yaml"
        echo ⚠️  请编辑 backend/config.yaml 文件，填入正确的阿里云API Key
    ) else (
        echo ❌ 配置文件模板不存在
    )
) else (
    echo ✅ 配置文件已存在
)

cd ..

REM 安装前端依赖
echo.
echo 安装前端依赖...
cd frontend
if not exist "package.json" (
    echo ❌ 前端项目配置文件不存在
    pause
    exit /b 1
)

npm install
if errorlevel 1 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
) else (
    echo ✅ 前端依赖安装成功
)

cd ..

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 下一步:
echo 1. 编辑 backend/config.yaml 文件，填入阿里云API Key
echo 2. 启动后端服务: cd backend && python start_server.py
echo 3. 启动前端服务: cd frontend && npm run dev
echo 4. 访问 http://localhost:5173 使用语音识别功能
echo.
echo 更多信息请查看 README.md 文件
echo.
pause
