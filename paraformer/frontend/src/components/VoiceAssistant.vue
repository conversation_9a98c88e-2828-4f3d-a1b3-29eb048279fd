<template>
  <div class="voice-assistant">
    <!-- 主录音按钮 -->
    <button
      :class="[
        'voice-button',
        {
          'recording': isRecording,
          'processing': isProcessing,
          'disabled': !isReady
        }
      ]"
      :disabled="!isReady"
      @click="toggleRecording"
      @touchstart="handleTouchStart"
      @touchend="handleTouchEnd"
    >
      <!-- 按钮内容 -->
      <div class="button-content">
        <!-- 麦克风图标 -->
        <div class="mic-icon" v-if="!isProcessing">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path v-if="!isRecording" d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
            <path v-if="!isRecording" d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
            <rect v-if="isRecording" x="6" y="6" width="12" height="12" rx="2"/>
          </svg>
        </div>
        
        <!-- 加载动画 -->
        <div class="loading-spinner" v-if="isProcessing">
          <div class="spinner"></div>
        </div>
      </div>
      
      <!-- 录音波纹动画 -->
      <div v-if="isRecording" class="recording-waves">
        <div class="wave wave-1"></div>
        <div class="wave wave-2"></div>
        <div class="wave wave-3"></div>
      </div>
      
      <!-- 脉冲动画 -->
      <div v-if="isRecording" class="pulse-rings">
        <div class="pulse-ring"></div>
        <div class="pulse-ring delay-1"></div>
        <div class="pulse-ring delay-2"></div>
      </div>
    </button>
    
    <!-- 状态文本 -->
    <div class="status-text" v-if="statusMessage">
      <p :class="statusType">{{ statusMessage }}</p>
    </div>
    
    <!-- 录音时长 -->
    <div class="recording-duration" v-if="isRecording">
      {{ formatDuration(recordingDuration) }}
    </div>
    
    <!-- 音频可视化 -->
    <div class="audio-visualizer" v-if="isRecording">
      <div class="visualizer-bars">
        <div 
          v-for="i in 20" 
          :key="i" 
          class="bar"
          :style="{ animationDelay: `${i * 0.1}s` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { createAudioRecorder, type AudioRecorder, type RecordingResult } from '../utils/audioRecorder';
import { apiService, type SpeechRecognitionResult } from '../utils/api';

// Props
interface Props {
  autoUpload?: boolean;
  longPressMode?: boolean; // 长按录音模式
}

const props = withDefaults(defineProps<Props>(), {
  autoUpload: true,
  longPressMode: false
});

// Emits
const emit = defineEmits<{
  recordingStart: [];
  recordingStop: [result: RecordingResult];
  recognitionSuccess: [result: SpeechRecognitionResult];
  recognitionError: [error: string];
}>();

// 响应式数据
const audioRecorder = ref<AudioRecorder | null>(null);
const isRecording = ref(false);
const isProcessing = ref(false);
const isReady = ref(false);
const recordingDuration = ref(0);
const statusMessage = ref('');
const statusType = ref<'info' | 'success' | 'error' | 'warning'>('info');

// 长按相关
const longPressTimer = ref<number | null>(null);
const isLongPress = ref(false);

// 定时器
let durationTimer: number | null = null;

// 计算属性
const buttonText = computed(() => {
  if (isProcessing.value) return '处理中...';
  if (isRecording.value) return '录音中';
  if (!isReady.value) return '初始化中...';
  return '点击录音';
});

// 方法
const showStatus = (message: string, type: typeof statusType.value = 'info', duration = 3000) => {
  statusMessage.value = message;
  statusType.value = type;
  
  setTimeout(() => {
    if (statusMessage.value === message) {
      statusMessage.value = '';
    }
  }, duration);
};

const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const startDurationTimer = () => {
  durationTimer = setInterval(() => {
    if (audioRecorder.value?.isRecording()) {
      recordingDuration.value = audioRecorder.value.getDuration();
    }
  }, 100);
};

const stopDurationTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer);
    durationTimer = null;
  }
  recordingDuration.value = 0;
};

// 触摸事件处理（长按模式）
const handleTouchStart = (event: TouchEvent) => {
  if (!props.longPressMode || !isReady.value) return;
  
  event.preventDefault();
  isLongPress.value = false;
  
  longPressTimer.value = setTimeout(() => {
    isLongPress.value = true;
    if (!isRecording.value) {
      startRecording();
    }
  }, 200);
};

const handleTouchEnd = (event: TouchEvent) => {
  if (!props.longPressMode) return;
  
  event.preventDefault();
  
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
    longPressTimer.value = null;
  }
  
  if (isLongPress.value && isRecording.value) {
    stopRecording();
  }
  
  isLongPress.value = false;
};

const toggleRecording = async () => {
  if (isProcessing.value || !isReady.value) return;
  
  // 长按模式下点击事件不处理录音
  if (props.longPressMode) return;

  try {
    if (isRecording.value) {
      await stopRecording();
    } else {
      await startRecording();
    }
  } catch (error) {
    console.error('录音操作失败:', error);
    showStatus(error instanceof Error ? error.message : '录音操作失败', 'error');
  }
};

const startRecording = async () => {
  if (!audioRecorder.value || !isReady.value) {
    throw new Error('录音器未就绪');
  }

  try {
    await audioRecorder.value.startRecording();
    isRecording.value = true;
    startDurationTimer();
    
    emit('recordingStart');
    showStatus('正在录音...', 'info', 0);
    
    // 添加触觉反馈（如果支持）
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : '开始录音失败');
  }
};

const stopRecording = async () => {
  if (!audioRecorder.value) {
    throw new Error('录音器未初始化');
  }

  isProcessing.value = true;
  stopDurationTimer();
  
  try {
    showStatus('正在处理录音...', 'info', 0);
    
    const result = await audioRecorder.value.stopRecording();
    isRecording.value = false;
    
    emit('recordingStop', result);
    
    // 添加触觉反馈
    if ('vibrate' in navigator) {
      navigator.vibrate([50, 50, 50]);
    }

    // 自动上传并识别
    if (props.autoUpload) {
      await uploadAndRecognize(result);
    } else {
      showStatus('录音完成', 'success');
    }
  } finally {
    isProcessing.value = false;
  }
};

const uploadAndRecognize = async (recordingResult: RecordingResult) => {
  try {
    showStatus('正在识别语音...', 'info', 0);
    
    // 获取音频格式信息
    const blobType = recordingResult.audioBlob.type;
    console.log('录音格式:', blobType);
    
    // 确定文件扩展名和类型
    let fileName = 'recording.wav';
    let fileType = 'audio/wav';
    
    if (blobType.includes('webm')) {
      fileName = 'recording.webm';
      fileType = blobType;
    } else if (blobType.includes('mp4')) {
      fileName = 'recording.mp4';
      fileType = blobType;
    } else if (blobType.includes('ogg')) {
      fileName = 'recording.ogg';
      fileType = blobType;
    }
    
    // 创建文件对象
    const file = new File([recordingResult.audioBlob], fileName, {
      type: fileType
    });

    // 调用识别 API
    const result = await apiService.recognizeSpeech(file);
    
    emit('recognitionSuccess', result);
    showStatus('识别完成', 'success');
    
  } catch (error) {
    console.error('语音识别失败:', error);
    const errorMessage = error instanceof Error ? error.message : '语音识别失败';
    emit('recognitionError', errorMessage);
    showStatus(errorMessage, 'error');
  }
};

// 生命周期
onMounted(async () => {
  try {
    // 检查浏览器支持
    if (!createAudioRecorder().constructor.isSupported()) {
      throw new Error('当前浏览器不支持录音功能');
    }

    // 创建录音器
    audioRecorder.value = createAudioRecorder({
      sampleRate: 16000,
      channelCount: 1
    });

    // 请求麦克风权限
    const hasPermission = await audioRecorder.value.requestPermission();
    if (!hasPermission) {
      throw new Error('无法获取麦克风权限');
    }

    isReady.value = true;
    showStatus('语音助手已就绪', 'success');
    
  } catch (error) {
    console.error('语音助手初始化失败:', error);
    showStatus(error instanceof Error ? error.message : '初始化失败', 'error');
  }
});

onUnmounted(() => {
  stopDurationTimer();
  
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
  }
  
  if (audioRecorder.value?.isRecording()) {
    audioRecorder.value.cancelRecording();
  }
});
</script>

<style scoped>
.voice-assistant {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

/* 主录音按钮 */
.voice-button {
  position: relative;
  width: 80px;
  height: 80px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.voice-button:hover:not(.disabled) {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);
}

.voice-button:active:not(.disabled) {
  transform: translateY(0) scale(0.98);
}

.voice-button.recording {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.6);
  animation: recordingPulse 2s infinite;
}

.voice-button.processing {
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
  box-shadow: 0 8px 32px rgba(254, 202, 87, 0.6);
}

.voice-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

/* 按钮内容 */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}

.mic-icon {
  width: 32px;
  height: 32px;
  transition: all 0.3s ease;
}

.mic-icon svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.voice-button.recording .mic-icon {
  animation: micBounce 1s infinite alternate;
}

/* 加载动画 */
.loading-spinner {
  width: 32px;
  height: 32px;
}

.spinner {
  width: 100%;
  height: 100%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 录音波纹动画 */
.recording-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: waveExpand 2s infinite;
}

.wave-2 {
  animation-delay: 0.5s;
}

.wave-3 {
  animation-delay: 1s;
}

/* 脉冲环动画 */
.pulse-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  border: 3px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: pulseRing 2s infinite;
}

.pulse-ring.delay-1 {
  animation-delay: 0.5s;
}

.pulse-ring.delay-2 {
  animation-delay: 1s;
}

/* 状态文本 */
.status-text {
  text-align: center;
  min-height: 1.5rem;
}

.status-text p {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-text .info {
  color: rgba(255, 255, 255, 0.8);
}

.status-text .success {
  color: #51cf66;
}

.status-text .error {
  color: #ff6b6b;
}

.status-text .warning {
  color: #feca57;
}

/* 录音时长 */
.recording-duration {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ff6b6b;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: durationPulse 1s infinite alternate;
}

/* 音频可视化 */
.audio-visualizer {
  width: 200px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.visualizer-bars {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 100%;
}

.bar {
  width: 3px;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 2px;
  animation: barDance 1.5s infinite ease-in-out;
  min-height: 4px;
}

/* 动画定义 */
@keyframes recordingPulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.6);
  }
  50% {
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.8), 0 0 0 10px rgba(255, 107, 107, 0.1);
  }
}

@keyframes micBounce {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes waveExpand {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.8);
    opacity: 0;
  }
}

@keyframes durationPulse {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

@keyframes barDance {
  0%, 100% {
    height: 20%;
  }
  50% {
    height: 100%;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voice-button {
    width: 70px;
    height: 70px;
  }

  .mic-icon {
    width: 28px;
    height: 28px;
  }

  .wave,
  .pulse-ring {
    width: 70px;
    height: 70px;
  }

  .audio-visualizer {
    width: 150px;
    height: 30px;
  }

  .recording-duration {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .voice-button {
    width: 60px;
    height: 60px;
  }

  .mic-icon {
    width: 24px;
    height: 24px;
  }

  .wave,
  .pulse-ring {
    width: 60px;
    height: 60px;
  }

  .audio-visualizer {
    width: 120px;
    height: 25px;
  }

  .status-text p {
    font-size: 0.75rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .voice-button:hover {
    transform: none;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  }

  .voice-button:active:not(.disabled) {
    transform: scale(0.95);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .voice-button {
    border: 2px solid white;
  }

  .status-text .info {
    color: white;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .voice-button,
  .mic-icon,
  .status-text p {
    transition: none;
  }

  .wave,
  .pulse-ring,
  .recording-duration,
  .bar {
    animation: none;
  }

  .voice-button.recording {
    animation: none;
  }
}
</style>
